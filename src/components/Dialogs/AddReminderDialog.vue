<template>
  <div v-if="show" class="dialog-overlay">
    <div class="dialog-container add-dialog">
      <div class="dialog-header">
        <div class="dialog-title">添加提醒</div>
        <div class="dialog-close" @click="handleClose">
          <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
        </div>
      </div>

      <div class="dialog-content add-content">
        <!-- 自然语言添加区域 -->
        <div class="natural-add-section">
          <!-- 输入提示 -->
          <div class="input-hint">
            <div class="hint-desc">让老董帮你记，后续提醒你：</div>
            <div class="hint-examples">
              <div class="example-item">• 明天上午9点开会，提前半小时提醒我</div>
              <div class="example-item">• 每周五下午5点提醒我写周报</div>
              <div class="example-item">• 下个月5号是妈妈生日，提前一天提醒</div>
            </div>
          </div>

          <!-- 文字输入框 -->
          <div class="input-group">
            <label class="input-label">提醒内容</label>
            <div class="input-content-wrapper">
              <textarea
                ref="textInputRef"
                v-model="inputText"
                class="input-field natural-input"
                :placeholder="isRecording ? '我在听，请说...' : '请用自然语言描述您的提醒需求...'"
                maxlength="200"
                rows="3"
              ></textarea>
              <!-- 麦克风按钮在输入框内部右侧 -->
              <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
                <MicrophoneIcon :size="16" />
              </div>
            </div>
            <div class="char-count">{{ inputText.length }}/200</div>
          </div>

          <!-- 提交按钮 -->
          <div class="submit-area">
            <button
              class="send-btn"
              :class="{ 'not-input': !inputText.trim() || isSubmitting }"
              :disabled="!inputText.trim() || isSubmitting"
              @click="handleSubmit"
            >
              <span v-if="isSubmitting">创建中...</span>
              <span v-else>创建提醒</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onBeforeUnmount, nextTick } from 'vue';
import { addReminderNatural, type IAddReminderNaturalRequest } from '@/apis/memory';
import { showSuccessToast, showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';

// Props定义
interface IProps {
  show: boolean;
  userId: string;
  personId?: string;
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  success: [];
}>();

// 状态管理
const inputText = ref('');
const isSubmitting = ref(false);
const textInputRef = ref(); // 输入框引用

// 语音输入相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const audioBufferIndex = ref(0);
const sessionId = ref('');
const lastBuffer = ref<ArrayBuffer | null>(null);
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 重置状态
const resetState = () => {
  inputText.value = '';
  recognizedText.value = '';
  isSubmitting.value = false;
  if (isRecording.value) {
    void stopVoiceRecording();
  }
  releaseMicrophoneResources();
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 在光标位置插入文字的工具函数（针对textarea元素）
const insertTextAtCursor = (newText: string) => {
  if (!textInputRef.value) return;

  const inputElement = textInputRef.value;
  const start = (inputElement.selectionStart as number) || 0;
  const end = (inputElement.selectionEnd as number) || 0;
  const currentValue = inputText.value;

  // 在光标位置插入新文字
  const newValue = currentValue.slice(0, start) + newText + currentValue.slice(end);
  inputText.value = newValue;

  // 更新光标位置到插入文字的末尾
  const newCursorPosition = start + newText.length;

  // 使用 nextTick 确保 DOM 更新后再设置光标位置
  void nextTick(() => {
    inputElement.setSelectionRange(newCursorPosition, newCursorPosition);
    inputElement.focus();
  });
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    void stopVoiceRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      // 修复：检查 full_text 而不是 text，并且确保 full_text 不为空且与当前值不同
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 在光标位置插入新文字
        if (textToInsert) {
          insertTextAtCursor(textToInsert);
        }

        lastVoiceText.value = newText;
        recognizedText.value = newText;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 处理语音按钮点击 - 参考inputBar逻辑
const handleVoiceButtonClick = async () => {
  if (isRecording.value) {
    await stopVoiceRecording();
  } else {
    await startVoiceRecording();
  }
};

// 开始语音录音
const startVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，停止录音
    await stopVoiceRecording();
    return;
  }

  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    // 重置语音识别状态
    lastVoiceText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 语音识别完成，文字已经通过 insertTextAtCursor 插入到光标位置
    // 不需要再次设置 inputText.value，避免覆盖用户可能的编辑
    console.log('📤 [AddReminderDialog] 语音识别完成，文字已插入到光标位置:', recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  recognizedText.value = '';
  lastVoiceText.value = '';
};

// 处理提交
const handleSubmit = () => {
  if (!inputText.value.trim() || isSubmitting.value) {
    return;
  }

  const content = inputText.value.trim();
  handleSubmitReminder(content);
};

// 提交提醒
const handleSubmitReminder = async (naturalText: string) => {
  if (!naturalText.trim() || isSubmitting.value) return;

  console.log('🚀 [AddReminderDialog] 开始提交自然语言提醒:', naturalText);

  isSubmitting.value = true;

  try {
    // 构建请求参数
    const requestData: IAddReminderNaturalRequest = {
      user_id: props.userId,
      natural_text: naturalText,
    };

    console.log('📤 [AddReminderDialog] 自然语言提醒请求参数:', requestData);

    // 调用自然语言添加提醒API
    const response = await addReminderNatural(requestData);

    console.log('📡 [AddReminderDialog] 自然语言提醒响应:', response);

    if (response && response.success) {
      console.log('✅ [AddReminderDialog] 提醒添加成功');
      showSuccessToast('提醒添加成功！');

      // 触发 success 事件通知父组件
      emit('success');

      // 发送自定义事件通知刷新ReminderSection
      window.dispatchEvent(
        new CustomEvent('addremindersuccess', {
          detail: {
            userId: props.userId,
            reminderId: response.reminder_id,
          },
        }),
      );

      // API 成功后关闭弹窗
      resetState();
      emit('close');
    } else {
      console.warn('⚠️ [AddReminderDialog] 添加提醒失败:', response);
      showToast('提醒添加失败，请重试');
    }
  } catch (error) {
    console.error('❌ [AddReminderDialog] 提交提醒失败:', error);
    showToast('提醒添加失败，请重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 监听show变化，重置状态
watch(
  () => props.show,
  (newValue) => {
    if (newValue) {
      resetState();
    }
  },
);

// 处理关闭
const handleClose = () => {
  if (isSubmitting.value) return;

  resetState();
  emit('close');
};

// 组件卸载时清理
onBeforeUnmount(() => {
  resetState();
});
</script>

<style scoped lang="scss">
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--overlay-dark);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: var(--bg-glass-popup);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid var(--accent-color);
  box-shadow: var(--shadow-accent);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 600px;
  color: var(--text-primary);
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
  // 提升字体清晰度
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;

  &.add-dialog {
    max-height: 90vh;
    overflow-y: auto;
  }
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 13px;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: var(--primary-color);
    font-size: 36px; // 增加4px (原来32px)
    font-weight: 600;
    flex-shrink: 0;
  }

  .dialog-close {
    background: var(--bg-glass);
    border: 1px solid var(--bg-glass-hover);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--bg-glass-hover);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  overflow-y: auto;

  &.add-content {
    padding-top: 0;
  }
}

// 自然语言添加区域
.natural-add-section {
  .input-hint {
    background: var(--primary-color-light);
    // 移除毛玻璃，避免文字发糊
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
    // 提升字体清晰度
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    // 默认文本颜色设为高对比
    color: var(--text-primary);
    border-left: 4px solid var(--accent-color);
    box-shadow: var(--shadow-accent);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 32px;
    max-height: 600px;
    overflow-y: auto;

    .hint-title {
      color: var(--primary-color);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 600;
      margin-bottom: 12px;
    }

    .hint-desc {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      margin-bottom: 16px;
    }

    .hint-examples {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .example-item {
        color: var(--person-detail-context);
        font-size: 28px; // 增加4px (原来22px)
      }
    }
  }

  // 语音识别文字显示
  .voice-text-display {
    background: var(--primary-color-light);
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    .voice-placeholder {
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来24px)
      font-style: italic;
    }

    .voice-message-text {
      color: var(--text-primary);
      font-size: 28px; // 增加4px (原来24px)
      line-height: 1.5;
      text-align: center;
    }
  }

  // 输入组
  .input-group {
    margin-bottom: 32px;
    position: relative;

    .input-label {
      display: block;
      color: var(--primary-color);
      font-size: 32px; // 增加4px (原来28px)
      font-weight: 500;
      margin-bottom: 12px;
    }

    .input-content-wrapper {
      width: 100%;
      position: relative;
      display: flex;
      align-items: stretch;

      .voice-toggle-inner {
        position: absolute;
        right: 16px;
        top: 24px;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--primary-color);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        z-index: 10;

        &.breathing {
          animation: breathing 2s ease-in-out infinite;
        }

        .iconfont {
          font-size: 28px;
          color: var(--primary-color);
        }
      }
    }

    .input-field {
      width: 100%;
      height: 300px;
      background: var(--bg-glass);
      border: 2px solid var(--bg-glass-hover);
      border-radius: 12px;
      padding: 16px 90px 16px 16px; // 右侧留出空间给麦克风按钮
      color: var(--person-detail-context);
      font-size: 28px; // 增加4px (原来26px)
      outline: none;
      transition: all 0.3s ease;
      box-sizing: border-box;
      // 让文本更清晰
      -webkit-font-smoothing: antialiased;
      text-rendering: optimizeLegibility;

      &::placeholder {
        color: var(--person-detail-context);
      }

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px var(--primary-color-medium);
      }

      &.natural-input {
        resize: vertical;
        min-height: 120px;
        line-height: 1.5;
      }
    }

    .char-count {
      position: absolute;
      bottom: 12px;
      right: 16px;
      color: var(--person-detail-context);
      font-size: 24px; // 增加4px (原来20px)
      pointer-events: none;
    }
  }

  // 提交按钮区域
  .submit-area {
    margin-top: 32px;
    display: flex;
    justify-content: center;

    .send-btn {
      flex: 1;
      padding: 16px 16px;
      border-radius: 20px;
      font-size: 36px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid var(--primary-color);
      background: var(--bg-glass);
      backdrop-filter: blur(10px);
      color: var(--primary-color);
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 200px;

      &:hover:not(.not-input) {
        background: var(--primary-color-medium);
        transform: translateY(-2px);
        box-shadow: var(--shadow-accent);
      }

      &.not-input {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 8px var(--primary-color-medium);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 var(--primary-color-strong);
  }
}
</style>
