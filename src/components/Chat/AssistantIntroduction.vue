<template>
  <div class="assistant-introduction-container">
    <!-- 助手介绍区域 -->
    <div class="assistant-desc-rotator">
      <div class="desc-content">
        <!-- 左侧活跃助手头像 -->
        <div
          v-if="activeAssistant"
          class="active-assistant-avatar"
          :class="{
            'avatar-entering': isAvatarEntering,
            'avatar-leaving': isAvatarLeaving,
          }"
        >
          <div class="avatar-circle">
            <img :src="activeAssistant.image" :alt="activeAssistant.title" class="avatar-img" />
          </div>
          <div class="assistant-name">{{ activeAssistant.title }}</div>
        </div>

        <!-- 右侧文字介绍 -->
        <div class="desc-text" :class="{ 'with-avatar': activeAssistant }">
          <strong class="desc-badge">各个助手的自我介绍<br /></strong>
          <span class="desc-content-text" :class="{ typing: isTyping }">{{ displayText }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';

// Props
interface IAssistantFeature {
  id: number;
  title: string;
  image: string;
}

interface IProps {
  assistantIntros: string[];
  availableFeatures: IAssistantFeature[];
  futureFeatures: IAssistantFeature[];
  topFeatures?: {
    id: number;
    title: string;
    image: string;
    processedImage?: string;
  }[];
}

const props = defineProps<IProps>();

// 状态管理
const currentIntroIndex = ref(0);
const displayText = ref('');
const activeAssistant = ref<IAssistantFeature | null>(null);
const isAvatarEntering = ref(false);
const isAvatarLeaving = ref(false);
const isTyping = ref(false);
const avatarStartPosition = ref<{ x: number; y: number } | null>(null);

// 计时器
let introTimer: number | null = null;
let typewriterTimer: number | null = null;

// 合并所有助手数据 - 按照用户期望的介绍顺序：第三行 → 第二行 → 第一行
const allAssistants = computed(() => {
  const tops = props.topFeatures
    ? props.topFeatures.map((t) => ({
        id: t.id,
        title: t.title,
        image: t.image,
      }))
    : [];
  // 合并顺序：第三行(availableFeatures) → 第二行(futureFeatures) → 第一行(topFeatures)
  return [...props.availableFeatures, ...props.futureFeatures, ...tops];
});

// 获取当前助手
const getCurrentAssistant = (index: number): IAssistantFeature | null => {
  // 按照用户期望的介绍顺序：第三行从左到右 → 第二行从左到右 → 第一行从左到右
  const assistantMapping = [
    // 第三行：董关系、董问题、董天奇
    { title: '董关系', originalTitle: '董关系' },
    { title: '董问题', originalTitle: '董问题' },
    { title: '董天奇', originalTitle: '董天奇' },
    // 第二行：董理财、董追剧、董健康、董搭配
    { title: '董理财', originalTitle: '董理财' },
    { title: '董追剧', originalTitle: '董追剧' },
    { title: '董健康', originalTitle: '董健康' },
    { title: '董搭配', originalTitle: '董搭配' },
    // 第一行：董学习、董出行、董健身、董外卖、董家务
    { title: '董学习', originalTitle: '董学习' },
    { title: '董出行', originalTitle: '董出行' },
    { title: '董健身', originalTitle: '董健身' },
    { title: '董外卖', originalTitle: '董外卖' },
    { title: '董家务', originalTitle: '董家务' },
  ];

  const mapping = assistantMapping[index];
  if (!mapping) return null;

  // 调试输出
  console.log(
    '🔍 [AssistantIntroduction] 查找助手:',
    mapping,
    'allAssistants:',
    allAssistants.value,
  );

  return allAssistants.value.find((assistant) => assistant.title === mapping.originalTitle) || null;
};

// 标记原位置头像被隐藏（在进入动画开始前调用，避免空窗）
const markOriginalAvatarHidden = (assistant: IAssistantFeature) => {
  const featureItems = document.querySelectorAll('.feature-item');
  featureItems.forEach((item) => {
    const label = item.querySelector('.feature-label') as HTMLElement;
    if (label && label.textContent?.trim() === assistant.title) {
      item.classList.add('avatar-moved');
    }
  });
};

// 清理所有残留的隐藏状态，避免上一轮未正确恢复导致其他行头像消失
const clearMovedAvatars = () => {
  const moved = document.querySelectorAll('.feature-item.avatar-moved');
  moved.forEach((el) => el.classList.remove('avatar-moved'));
};

// 等待DOM元素准备就绪的辅助函数
const waitForElements = (selector: string, maxWait = 3000): Promise<NodeListOf<Element>> => {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();

    const checkElements = () => {
      const elements = document.querySelectorAll(selector);
      if (elements.length > 0) {
        // 额外检查元素是否真正可见和有尺寸
        const firstElement = elements[0] as HTMLElement;
        const rect = firstElement.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          resolve(elements);
          return;
        }
      }

      if (Date.now() - startTime > maxWait) {
        console.warn(`⚠️ [AssistantIntroduction] 等待元素超时: ${selector}`);
        reject(new Error(`Elements not ready: ${selector}`));
        return;
      }

      requestAnimationFrame(checkElements);
    };

    checkElements();
  });
};

// 隐藏/显示原位置的功能头像
const hideOriginalAvatar = async (assistant: IAssistantFeature) => {
  if (!assistant) return;

  try {
    // 等待feature-item元素准备就绪
    const featureItems = await waitForElements('.feature-item', 2000);

    // 在每次查找前重置起点坐标，避免使用到上一轮的旧值
    avatarStartPosition.value = null;

    featureItems.forEach((item) => {
      const img = item.querySelector('.feature-avatar-img') as HTMLImageElement;
      const label = item.querySelector('.feature-label') as HTMLElement;

      if (img && label && label.textContent?.trim() === assistant.title) {
        console.log('🫥 [AssistantIntroduction] 隐藏原位置头像:', assistant.title);

        // 获取原位置坐标 - 简化计算，直接相对于视窗
        const rect = item.getBoundingClientRect();
        const targetRect = document.querySelector('.desc-text')?.getBoundingClientRect();

        if (targetRect) {
          avatarStartPosition.value = {
            // 从原头像中心到目标位置的偏移
            x: rect.left + rect.width / 2 - (targetRect.left - 50), // 调整目标位置，给头像更多空间
            y: rect.top + rect.height / 2 - (targetRect.top + 30), // 调整垂直位置
          };
          console.log('📍 [AssistantIntroduction] 原位置坐标:', avatarStartPosition.value);
        } else {
          console.warn('⚠️ [AssistantIntroduction] 未获取到目标文字框位置，跳过隐藏原头像');
        }

        // 立即隐藏原位置头像，避免闪烁
        item.classList.add('avatar-moved');
      }
    });
  } catch (error) {
    console.warn('⚠️ [AssistantIntroduction] 等待feature-item元素失败:', error);
    // 降级处理：直接查询现有元素
    const featureItems = document.querySelectorAll('.feature-item');
    if (featureItems.length > 0) {
      console.log('🔄 [AssistantIntroduction] 降级处理：使用现有元素');
      featureItems.forEach((item) => {
        const label = item.querySelector('.feature-label') as HTMLElement;
        if (label && label.textContent?.trim() === assistant.title) {
          item.classList.add('avatar-moved');
        }
      });
    }
  }
};

const showOriginalAvatar = (assistant: IAssistantFeature) => {
  if (!assistant) return;

  // 查找所有功能头像，显示匹配的那个
  const featureItems = document.querySelectorAll('.feature-item');
  featureItems.forEach((item) => {
    const img = item.querySelector('.feature-avatar-img') as HTMLImageElement;
    const label = item.querySelector('.feature-label') as HTMLElement;

    if (img && label && label.textContent?.trim() === assistant.title) {
      console.log('👀 [AssistantIntroduction] 显示原位置头像:', assistant.title);
      item.classList.remove('avatar-moved');
    }
  });
};

// 打字机效果
const typeText = (text: string): Promise<void> => {
  return new Promise((resolve) => {
    displayText.value = '';
    isTyping.value = true;
    let charIndex = 0;

    const typeChar = () => {
      if (charIndex < text.length) {
        displayText.value += text[charIndex];
        charIndex++;
        typewriterTimer = window.setTimeout(typeChar, 80); // 调整打字速度，平衡动画效果和用户体验
      } else {
        isTyping.value = false;
        resolve();
      }
    };

    typeChar();
  });
};

// 头像进入动画
const showAssistantAvatar = async (assistant: IAssistantFeature): Promise<void> => {
  console.log('🎬 [AssistantIntroduction] 显示助手头像:', assistant);

  // 隐藏原位置的头像并获取坐标
  // 先清理可能残留的状态，防止影响本轮
  clearMovedAvatars();
  await hideOriginalAvatar(assistant);

  return new Promise((resolve) => {
    // 先设置助手数据，但不立即显示
    activeAssistant.value = assistant;

    // 使用nextTick确保DOM完全更新后再设置位置和开始动画
    void nextTick(() => {
      const avatarElement = document.querySelector('.active-assistant-avatar') as HTMLElement;
      if (avatarElement && avatarStartPosition.value) {
        // 先设置起始位置，确保头像出现在正确的起始位置
        avatarElement.style.setProperty('--start-x', `${avatarStartPosition.value.x}px`);
        avatarElement.style.setProperty('--start-y', `${avatarStartPosition.value.y}px`);

        // 立即应用起始位置的transform，防止闪烁
        avatarElement.style.transform = `translate(${avatarStartPosition.value.x}px, ${avatarStartPosition.value.y}px)`;
        avatarElement.style.opacity = '1';
        avatarElement.style.visibility = 'visible';

        console.log('🎬 [AssistantIntroduction] 设置动画起始位置:', avatarStartPosition.value);

        // 使用双重requestAnimationFrame确保样式完全应用后再开始动画
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            // 清除手动设置的transform，让CSS动画接管
            avatarElement.style.transform = '';

            isAvatarEntering.value = true;
            isAvatarLeaving.value = false;

            console.log('🎬 [AssistantIntroduction] 头像状态设置完成:', {
              activeAssistant: activeAssistant.value,
              isAvatarEntering: isAvatarEntering.value,
              isAvatarLeaving: isAvatarLeaving.value,
            });
          });
        });
      } else {
        console.warn('⚠️ [AssistantIntroduction] 头像元素或起始位置缺失，跳过动画');
        // 即使没有动画，也要设置基本状态
        isAvatarEntering.value = false;
        isAvatarLeaving.value = false;
      }
    });

    // 动画完成后解析Promise，头像保持在位置
    setTimeout(() => {
      isAvatarEntering.value = false;
      console.log('🎬 [AssistantIntroduction] 头像进入动画完成，头像保持在位置');
      resolve();
    }, 850); // 800ms动画 + 50ms缓冲
  });
};

// 头像离开动画
const hideAssistantAvatar = async (): Promise<void> => {
  return new Promise((resolve) => {
    if (!activeAssistant.value) {
      resolve();
      return;
    }

    const currentAssistant = activeAssistant.value;
    isAvatarLeaving.value = true;

    setTimeout(() => {
      // 恢复原位置头像的显示
      showOriginalAvatar(currentAssistant);

      activeAssistant.value = null;
      isAvatarEntering.value = false;
      isAvatarLeaving.value = false;
      resolve();
    }, 800); // 与CSS动画时间一致
  });
};

// 播放单个助手介绍
const playAssistantIntro = async (index: number) => {
  const assistant = getCurrentAssistant(index);
  const introText = props.assistantIntros[index];

  console.log(
    '🎭 [AssistantIntroduction] 开始播放助手介绍:',
    index,
    assistant,
    introText ? `${introText.substring(0, 20)}...` : '无介绍文本',
  );

  if (!assistant || !introText) {
    console.warn('⚠️ [AssistantIntroduction] 助手或介绍文本缺失:', {
      assistant,
      hasIntroText: !!introText,
    });
    // 避免上一轮的头像仍处于隐藏状态
    clearMovedAvatars();
    activeAssistant.value = null;
    return;
  }

  // 1. 头像移动到位置并等待动画完成
  console.log('🎭 步骤1: 头像移动到位置');
  await showAssistantAvatar(assistant);

  // 2. 立即开始打字机效果（头像已经在目标位置）
  console.log('🎭 步骤2: 开始打字机效果');
  await typeText(introText);

  // 3. 打字完成，等待3秒让用户阅读
  console.log('🎭 步骤3: 打字完成，等待阅读时间');
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 3000);
  });

  // 4. 头像移回原位
  console.log('🎭 步骤4: 头像移回原位');
  await hideAssistantAvatar();

  // 5. 停顿1秒再开始下一个
  console.log('🎭 步骤5: 停顿后开始下一个');
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 1000);
  });
};

// 开始介绍循环
/* eslint-disable no-await-in-loop */
const startIntroductionCycle = async () => {
  // 首次启动时等待1秒
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 1000);
  });

  while (isComponentActive.value) {
    for (let i = 0; i < props.assistantIntros.length && isComponentActive.value; i++) {
      currentIntroIndex.value = i;
      // eslint-disable-next-line no-await-in-loop
      await playAssistantIntro(i);

      // 检查组件是否仍然活跃
      if (!isComponentActive.value) {
        console.log('🛑 [AssistantIntroduction] 组件已不活跃，停止介绍循环');
        break;
      }
    }

    // 完成一轮后暂停3秒再开始下一轮，但要检查组件状态
    if (isComponentActive.value) {
      // eslint-disable-next-line no-await-in-loop
      await new Promise((resolve) => {
        setTimeout(() => resolve(undefined), 3000);
      });
    }
  }

  console.log('🔄 [AssistantIntroduction] 介绍循环已停止');
};
/* eslint-enable no-await-in-loop */

// 清理定时器
const clearTimers = () => {
  if (introTimer) {
    window.clearTimeout(introTimer);
    introTimer = null;
  }
  if (typewriterTimer) {
    window.clearTimeout(typewriterTimer);
    typewriterTimer = null;
  }
};

// 组件状态管理
const isComponentActive = ref(false);

// 启动介绍循环的函数
const initializeIntroduction = async () => {
  // 等待页面完全稳定
  await new Promise((resolve) => {
    setTimeout(() => resolve(undefined), 1000);
  });

  // 确保feature-item元素已经渲染
  try {
    await waitForElements('.feature-item', 3000);
    console.log('✅ [AssistantIntroduction] feature-item元素已准备就绪');
  } catch (error) {
    console.warn('⚠️ [AssistantIntroduction] feature-item元素等待超时，继续执行');
  }

  // 清理可能残留的状态
  clearMovedAvatars();

  // 开始介绍循环
  if (isComponentActive.value) {
    console.log('🚀 [AssistantIntroduction] 开始介绍循环');
    void startIntroductionCycle();
  }
};

onMounted(() => {
  console.log('🚀 [AssistantIntroduction] 组件挂载，props:', props);
  isComponentActive.value = true;

  // 使用更稳健的初始化流程
  void initializeIntroduction();
});

onUnmounted(() => {
  console.log('🔄 [AssistantIntroduction] 组件卸载，清理状态');
  isComponentActive.value = false;
  clearTimers();
  clearMovedAvatars();

  // 重置组件状态
  activeAssistant.value = null;
  isAvatarEntering.value = false;
  isAvatarLeaving.value = false;
  displayText.value = '';
  avatarStartPosition.value = null;
});
</script>

<style lang="scss" scoped>
.assistant-introduction-container {
  width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
  margin-top: 20px; // 调整上边距以匹配原设计
  margin-bottom: 0; // 移除下边距，让组件更紧凑
}

.assistant-desc-rotator {
  position: relative;
  width: 100%;
  max-width: 600px; // 调整最大宽度以匹配原设计
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  padding: 0; // 移除内边距，让布局更紧凑
}

.desc-content {
  display: flex;
  align-items: center;
  gap: 16px;
  position: relative;
}

.active-assistant-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  left: -25px; // 减少距离，让头像更靠近卡片
  top: 20px; // 调整顶部位置
  opacity: 0;
  visibility: hidden; // 添加visibility确保完全不可见
  z-index: 1000;
  pointer-events: none;

  &.avatar-entering {
    opacity: 1;
    visibility: visible; // 动画时显示
    animation: avatarSlideIn 0.8s ease-out forwards;
  }

  &.avatar-leaving {
    opacity: 1;
    visibility: visible; // 动画时显示
    animation: avatarSlideOut 0.8s ease-in forwards;
  }

  &:not(.avatar-entering):not(.avatar-leaving) {
    opacity: 1;
    visibility: visible; // 静止状态时显示
    transform: translate(-30px, 0px);
  }

  .avatar-circle {
    width: 86px; // 匹配功能项头像大小
    height: 86px; // 匹配功能项头像大小
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.75); // 匹配功能项边框样式
    margin-bottom: 10px; // 调整间距

    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .assistant-name {
    font-size: 24px; // 调整字体大小以匹配功能标签
    font-weight: 500;
    color: rgb(0, 0, 0);
    white-space: nowrap;
  }
}

.desc-text {
  display: inline-block;
  font-size: 24px; // 调整字体大小以匹配原设计
  font-weight: 600; // 调整字体粗细
  color: #333333; // 固定黑色字体，不跟随主题变化
  line-height: 1.5;
  padding: 16px 20px;
  border-radius: 12px;
  background: #fff3a0; // 固定黄色背景，类似便签纸
  border: 1px solid #e6d875; // 稍深的黄色边框
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); // 添加阴影增强备忘录效果
  width: 500px;
  max-width: 90vw;
  min-width: 300px;
  white-space: normal;
  word-break: break-word;
  position: relative;
  text-align: center; // 改为居中对齐以匹配原设计
  box-sizing: border-box;

  &.with-avatar {
    margin-left: 60px; // 增加左边距为头像留出更多空间
    text-align: left; // 有头像时左对齐
  }

  .desc-badge {
    color: #2c2c2c; // 固定深黑色标题，不跟随主题变化
    font-weight: 600;
    font-size: 28px; // 调整标题字体大小
    display: block;
    text-align: center;
    margin-bottom: 8px;
  }

  .desc-content-text {
    min-height: 1.6em;
    text-align: left; // 内容文字左对齐
    display: block;
    font-size: 24px; // 确保内容字体大小一致
    color: #333333; // 固定黑色内容文字
  }
}

// 简化的头像动画
@keyframes avatarSlideIn {
  0% {
    opacity: 1;
    transform: translate(var(--start-x, 0px), var(--start-y, 0px));
  }
  100% {
    opacity: 1;
    transform: translate(-30px, 0px); // 调整目标位置
  }
}

@keyframes avatarSlideOut {
  0% {
    opacity: 1;
    transform: translate(-30px, 0px); // 调整起始位置
  }
  100% {
    opacity: 1;
    transform: translate(var(--start-x, 0px), var(--start-y, 0px));
  }
}

// 打字机光标效果
.desc-content-text.typing::after {
  content: '|';
  animation: blink 1s infinite;
  color: currentColor;
  font-weight: 800;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// 隐藏被移动的原位置头像
:global(.feature-item.avatar-moved) {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
}
</style>
