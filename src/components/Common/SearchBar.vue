<template>
  <div class="search-container">
    <div class="search-input-wrapper">
      <div class="input-container">
        <div class="voice-input-wrapper">
          <input
            v-model="searchQuery"
            type="text"
            class="search-input"
            :placeholder="isRecording ? '我在听，请说...' : placeholder"
            maxlength="50"
            @input="onSearchInput"
          />
          <!-- 清除按钮在语音按钮左侧 -->
          <button v-show="searchQuery.trim()" class="clear-btn" @click="handleClear">
            <DeleteIcon :size="16" color="var(--primary-color)" />
          </button>
          <!-- 麦克风按钮在输入框右侧 -->
          <div class="voice-toggle-inner" :class="{ breathing: isRecording }" @click="handleVoiceButtonClick">
            <MicrophoneIcon :size="16" />
          </div>
        </div>
      </div>
      <button class="add-person-btn-header" @click="handleAdd">
        <PlusIcon class="add-icon" :size="24" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount } from 'vue';
import { showToast } from 'vant';
import { getStreamAsr } from '@/apis/chat';
import Recorder from 'recorder-realtime';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import PlusIcon from '@/assets/icons/PlusIcon.vue';
import DeleteIcon from '@/assets/icons/DeleteIcon.vue';
import MicrophoneIcon from '@/assets/icons/MicrophoneIcon.vue';

// Props
interface IProps {
  placeholder?: string;
}

withDefaults(defineProps<IProps>(), {
  placeholder: '请输入姓名进行搜索',
});

// Emits
const emit = defineEmits<{
  search: [query: string];
  add: [];
  input: [query: string];
}>();

// 响应式数据
const searchQuery = ref('');
let searchTimer: NodeJS.Timeout | null = null;

// 语音录音相关变量
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 语音录音响应式数据
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref();
const voiceMessage = ref('');
const isRecording = ref(false);
const lastVoiceText = ref(''); // 上次语音识别的文字，用于增量更新

// 处理搜索
const handleSearch = (query: string) => {
  if (query.trim()) {
    emit('search', query.trim());
  }
};

// 处理添加
const handleAdd = () => {
  emit('add');
};

// 处理清空
const handleClear = () => {
  searchQuery.value = '';
  // 清除搜索定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
    searchTimer = null;
  }
  // 通知父组件输入变化
  emit('input', '');
};

// 处理输入 - 实现防抖搜索
const onSearchInput = () => {
  emit('input', searchQuery.value);

  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，2秒后执行搜索
  searchTimer = setTimeout(() => {
    handleSearch(searchQuery.value);
  }, 1000);
};

// 语音录音相关方法
// 设置麦克风权限
async function setMicPermission() {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
}

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (
        streamData.data.full_text &&
        streamData.data.full_text.trim() !== '' &&
        streamData.data.full_text !== lastVoiceText.value
      ) {
        // 计算新增的文字部分
        const newText = streamData.data.full_text;
        const previousText = lastVoiceText.value;

        // 如果新文字包含之前的文字，只插入新增部分
        let textToInsert = newText;
        if (previousText && newText.startsWith(previousText)) {
          textToInsert = newText.slice(previousText.length);
        }

        // 更新搜索输入框
        if (textToInsert) {
          updateVoiceInput(textToInsert);
        }

        lastVoiceText.value = newText;
        voiceMessage.value = newText;
        await autoStopTimeout();
      }
    }
  };
};

// 更新语音输入到搜索框
const updateVoiceInput = (text: string) => {
  searchQuery.value += text;
  // 触发输入事件
  onSearchInput();
};

// 开始录音
async function startRecording() {
  if (isRecording.value) {
    await stopRecording();
    return;
  }

  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    // 重置语音识别状态
    lastVoiceText.value = '';
    voiceMessage.value = '';
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopRecording();
    }, 1000 * 60);
  }
}

// 两秒不说话自动停止
const autoStopTimeout = debounce(async () => {
  await stopRecording();
}, 2000);

// 释放麦克风资源
function releaseMicrophoneResources() {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => {
      track.stop();
    });
    mediaStream = null;
    micPermission.value = false;
  }
}

// 取消录音
function cancelRecording() {
  if (recorder) {
    recorder.stop();
  }
  isRecording.value = false;
  voiceMessage.value = '';
  releaseMicrophoneResources();
}

// 结束录音
async function stopRecording() {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });
  if (voiceMessage.value) {
    console.log('📤 [SearchBar] 语音识别完成:', voiceMessage.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
  // 清空语音消息和上次识别文字
  voiceMessage.value = '';
  lastVoiceText.value = '';
}

// 处理语音按钮点击
const handleVoiceButtonClick = async () => {
  await startRecording();
};

// 组件卸载时释放麦克风资源
onBeforeUnmount(() => {
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();
});

// 暴露方法供父组件调用
defineExpose({
  clearSearch: () => {
    searchQuery.value = '';
    // 清除搜索定时器
    if (searchTimer) {
      clearTimeout(searchTimer);
      searchTimer = null;
    }
  },
  getSearchQuery: () => searchQuery.value,
});
</script>

<style lang="scss" scoped>
.search-container {
  padding: 18px 0px;

  .search-input-wrapper {
    display: flex;
    gap: 20px;
    align-items: center;

    .input-container {
      flex: 1;
      position: relative;
      display: flex;
      align-items: center;

      .voice-input-wrapper {
        position: relative;
        width: 100%;
        display: flex;
        align-items: center;

        .voice-toggle-inner {
          position: absolute;
          right: 16px;
          top: 50%;
          transform: translateY(-50%);
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 50%;
          background: var(--bg-glass);
          border: 2px solid var(--border-accent);
          backdrop-filter: blur(20px);
          transition: all 0.3s ease;
          z-index: 10;

          &.breathing {
            animation: breathing 2s ease-in-out infinite;
          }

          .iconfont {
            font-size: 20px;
            color: var(--text-primary);
          }
        }

        .search-input {
          width: 100%;
          background: var(--bg-input);
          border: 2px solid var(--primary-color);
          border-radius: 18px;
          padding: 16px 20px;
          padding-right: 120px; // 右侧留出更多空间给清除按钮和麦克风按钮
          color: #000000;
          font-size: 26px; // 增大字体，原来是22px
          box-sizing: border-box;
          transition: all 0.2s ease;

          &::placeholder {
            color: #888888;
          }

          &:focus {
            outline: none;
            border-color: var(--accent-color);
            background: var(--bg-input-focus);
          }
        }
      }

      .clear-btn {
        position: absolute;
        right: 66px; // 调整位置，在语音按钮左侧
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--text-tertiary);
        font-size: 24px;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: var(--bg-glass);
          color: var(--text-secondary);
        }
      }
    }

    .add-person-btn-header {
      padding: 16px 16px;
      border-radius: 50%;
      font-size: 28px;
      font-weight: 600;
      cursor: pointer;
      border: 2px solid;
      color: var(--primary-color);
      border-color: var(--primary-color);
      background: var(--primary-color-medium);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      min-width: 60px;
      min-height: 60px; // 与search-input高度一致
      max-width: 60px;
      max-height: 50px;
      box-sizing: border-box;

      .add-icon {
        font-size: 32px;
        line-height: 1;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }
  }
}

@keyframes breathing {
  0% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--accent-color-strong);
  }
  50% {
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 0 8px var(--accent-color-light);
  }
  100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 0 var(--accent-color-strong);
  }
}
</style>
